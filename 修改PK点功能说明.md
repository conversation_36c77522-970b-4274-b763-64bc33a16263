# 修改PK点功能实现说明

## 🎯 功能概述

新增了修改PK点的功能，允许玩家直接修改游戏中的PK点数值。PK点是《西游大战僵尸2》中用于PK模式的重要属性。

## 📋 实现详情

### 1. **Shell端实现** (`shell/scripts/shell_fla/MainTimeline.as`)

#### 新增功能类型
- **功能编号**: 26
- **功能名称**: 修改PK点
- **实现函数**: `modifyPKPointFunction(player:Object, pkPoint:int)`

#### 实现逻辑
```actionscript
// 在switch语句中添加
case 26: // 修改PK点
   modifyPKPointFunction(player, parseInt(itemNum));
   break;
```

#### 核心功能函数
```actionscript
private function modifyPKPointFunction(player:Object, pkPoint:int):void
{
   // 1. 获取玩家的PKVO对象
   var pkVO:Object = player.getPKVO();
   
   // 2. 多种方式尝试修改PK点
   // 方法1: 通过setPKPoint方法
   pkVO.setPKPoint(pkPoint);
   
   // 方法2: 直接修改属性
   pkVO.pkPoint = pkPoint;
   
   // 方法3: 通过_antiwear修改
   pkVO._antiwear.pkPoint = pkPoint;
   
   // 3. 验证修改结果并刷新UI
}
```

### 2. **LocalconT端实现** (`localcon_T/scripts/localcon_T_fla/MainTimeline.as`)

#### 功能菜单更新
- 在`getFunctionName`函数中添加: `"修改PK点" // 26`
- 在下拉菜单数据中添加: `{"label":"修改PK点", "data":26}`
- 在`one_change`函数中添加对应的case处理

## 🔧 使用方法

### 步骤1: 编译更新
1. 重新编译 `shell/scripts/shell_fla/MainTimeline.as` 为 `shell.swf`
2. 重新编译 `localcon_T/scripts/localcon_T_fla/MainTimeline.as` 为 `localcon_T.swf`

### 步骤2: 运行外挂
1. 先运行 `shell.swf`
2. 再运行 `localcon_T.swf`

### 步骤3: 使用功能
1. **选择玩家**: P1 或 P2
2. **选择功能**: "修改PK点" (第26项)
3. **输入数值**: 在"数量"字段输入目标PK点数值
4. **点击发送**: 执行修改

## 📝 使用示例

### 示例1: 设置PK点为1000
```
玩家: P1
功能: 修改PK点 (第26项)
ID: (留空)
数量: 1000
```

### 示例2: 设置PK点为99999
```
玩家: P1
功能: 修改PK点 (第26项)
ID: (留空)
数量: 99999
```

## 🔍 技术细节

### PK点数据结构
- **类名**: `PKVO` (位于 `UI.Players` 包)
- **存储方式**: 通过 `_antiwear.pkPoint` 加密存储
- **访问方法**: 
  - `getPKPoint()`: 获取当前PK点
  - `setPKPoint(value)`: 设置PK点值

### 修改策略
1. **优先使用官方方法**: `setPKPoint()` 方法
2. **直接属性修改**: 直接设置 `pkPoint` 属性
3. **绕过加密修改**: 通过 `_antiwear.pkPoint` 修改

### 调试信息
功能执行时会输出详细的调试信息：
```
[DEBUG] 开始修改PK点，目标值: 1000
[DEBUG] 通过getPKVO()获取PKVO对象: 成功
[DEBUG] 原始PK点: 0
[DEBUG] 通过setPKPoint修改成功
[DEBUG] 修改后PK点: 1000
[DEBUG] PK点修改成功！
```

## ⚠️ 注意事项

### 1. **数值范围**
- 建议PK点设置在合理范围内 (0-99999)
- 过大的数值可能导致游戏异常

### 2. **游戏版本兼容性**
- 功能基于当前游戏版本的PKVO类实现
- 如果游戏更新可能需要调整代码

### 3. **使用建议**
- 建议在离线模式下使用
- 使用前备份游戏存档
- 观察调试输出确认修改成功

## 🎉 功能特点

### ✅ **多重保障**
- 三种不同的修改方式确保成功率
- 详细的错误处理和调试信息
- 修改结果验证机制

### ✅ **用户友好**
- 简单的界面操作
- 清晰的功能命名
- 详细的使用说明

### ✅ **技术可靠**
- 基于游戏源码分析实现
- 遵循游戏内部数据结构
- 完整的异常处理机制

## 📊 测试建议

### 基础测试
1. 设置PK点为100，验证修改成功
2. 设置PK点为0，验证可以清零
3. 设置较大数值，验证稳定性

### 进阶测试
1. 在PK模式中验证PK点是否生效
2. 保存游戏后重新加载验证持久性
3. 多次修改验证功能稳定性

## 🔄 后续扩展

可以考虑添加的相关功能：
- 修改PK胜负记录
- 修改PK排名
- 批量PK数据修改
- PK模式相关属性修改

---

**版本**: v1.0  
**更新日期**: 2024年  
**兼容性**: 《西游大战僵尸2》当前版本
