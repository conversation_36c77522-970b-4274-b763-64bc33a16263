# PK点修改功能快速测试指南

## 🚀 快速开始

### 1. 文件更新
确保你有最新的增强版文件：
- ✅ `shell/scripts/shell_fla/MainTimeline.as` (已添加修改PK点功能)
- ✅ `localcon_T/scripts/localcon_T_fla/MainTimeline.as` (已添加PK点选项)

### 2. 编译步骤
1. 使用Flash开发工具重新编译AS文件为SWF
2. 替换原来的 `shell.swf` 和 `localcon_T.swf`

### 3. 运行测试
1. 先运行 `shell.swf`
2. 再运行 `localcon_T.swf`

## 🧪 测试用例

### 测试1: 基础PK点修改
```
目标: 验证基本的PK点修改功能
操作:
- 玩家: P1
- 功能: 修改PK点 (第26项)
- ID: (留空)
- 数量: 100

预期结果:
- 调试信息显示修改成功
- PK点从原值变为100
```

### 测试2: 大数值PK点
```
目标: 测试大数值的稳定性
操作:
- 玩家: P1
- 功能: 修改PK点 (第26项)
- ID: (留空)
- 数量: 99999

预期结果:
- 成功设置为99999
- 游戏运行稳定
```

### 测试3: 清零PK点
```
目标: 验证可以将PK点设为0
操作:
- 玩家: P1
- 功能: 修改PK点 (第26项)
- ID: (留空)
- 数量: 0

预期结果:
- PK点成功清零
- 功能正常工作
```

### 测试4: 双玩家测试
```
目标: 验证P2玩家的PK点修改
操作:
- 玩家: P2
- 功能: 修改PK点 (第26项)
- ID: (留空)
- 数量: 500

预期结果:
- P2的PK点成功修改
- 不影响P1的数据
```

## 🔍 调试信息解读

### ✅ 成功的日志模式
```
[DEBUG] === 开始执行外挂功能 ===
[DEBUG] 开始修改PK点，目标值: 100
[DEBUG] 通过getPKVO()获取PKVO对象: 成功
[DEBUG] 原始PK点: 0
[DEBUG] 通过setPKPoint修改成功
[DEBUG] 修改后PK点: 100
[DEBUG] PK点修改成功！
[DEBUG] 刷新UI完成
[DEBUG] === 外挂功能执行结束 ===
```

### ❌ 失败的日志模式
```
[DEBUG] === 开始执行外挂功能 ===
[DEBUG] 开始修改PK点，目标值: 100
[DEBUG] 通过getPKVO()获取PKVO对象: 失败
[DEBUG] 错误：无法获取PKVO对象
[DEBUG] === 外挂功能执行结束 ===
```

## 🛠️ 故障排除

### 问题1: 无法获取PKVO对象
**症状**: 调试信息显示"无法获取PKVO对象"
**解决方案**:
1. 确保游戏已完全加载
2. 检查玩家对象是否有效
3. 尝试重新启动游戏

### 问题2: PK点修改失败
**症状**: 调试信息显示修改成功但数值未变
**解决方案**:
1. 检查游戏版本兼容性
2. 尝试重新加载游戏存档
3. 确认PKVO对象结构未变化

### 问题3: 界面无响应
**症状**: 点击发送按钮无反应
**解决方案**:
1. 检查LocalConnection连接
2. 重新启动shell.swf
3. 确认编译版本正确

## 📊 验证方法

### 方法1: 调试信息验证
- 观察控制台输出的调试信息
- 确认"PK点修改成功！"消息
- 检查修改前后的数值对比

### 方法2: 游戏内验证
- 进入PK模式查看PK点显示
- 检查角色属性面板
- 验证PK相关功能是否正常

### 方法3: 存档验证
- 保存游戏后重新加载
- 确认PK点数值持久保存
- 验证数据完整性

## ⚠️ 重要提醒

### 使用前准备
1. **备份存档**: 修改前务必备份游戏存档
2. **离线模式**: 建议在离线模式下测试
3. **版本检查**: 确认游戏版本与代码兼容

### 测试注意事项
1. **逐步测试**: 先测试小数值，再测试大数值
2. **观察日志**: 密切关注调试输出信息
3. **稳定性**: 每次修改后观察游戏稳定性

### 数值建议
- **安全范围**: 0-10000 (推荐日常使用)
- **测试范围**: 10000-99999 (功能测试)
- **避免**: 负数或超大数值

## 🎯 测试清单

- [ ] 基础PK点修改 (100)
- [ ] 大数值测试 (99999)
- [ ] 清零测试 (0)
- [ ] P2玩家测试
- [ ] 多次修改稳定性测试
- [ ] 存档持久性验证
- [ ] 游戏内PK模式验证
- [ ] 异常情况处理测试

## 📈 性能指标

### 成功率目标
- **基础修改**: 100% 成功率
- **大数值**: 95% 成功率
- **异常处理**: 100% 不崩溃

### 响应时间
- **修改操作**: < 1秒
- **UI刷新**: < 2秒
- **验证确认**: < 1秒

---

**测试版本**: v1.0  
**测试日期**: 2024年  
**测试环境**: Windows + Flash Player
